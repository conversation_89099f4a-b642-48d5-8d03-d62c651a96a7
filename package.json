{"name": "news-scraper-api", "private": true, "type": "module", "scripts": {"start": "tsx src/index.ts", "dev": "tsx watch src/index.ts", "build": "tsc", "vercel-build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@hono/node-server": "^1.17.1", "axios": "^1.6.0", "dotenv": "^16.0.0", "elysia": "^1.0.0", "jsdom": "^26.1.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "rotating-file-stream": "^3.0.0", "winston": "^3.0.0", "tsx": "^4.20.3"}, "packageManager": "pnpm@9.13.0+sha512.beb9e2a803db336c10c9af682b58ad7181ca0fbd0d4119f2b33d5f2582e96d6c0d93c85b23869295b765170fbdaa92890c0da6ada457415039769edf3c959efe", "devDependencies": {"@types/jsdom": "^21.1.7", "@types/node": "^24.0.15", "typescript": "^5.8.3"}}