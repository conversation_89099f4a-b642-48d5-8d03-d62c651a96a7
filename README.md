# News Scraper API

A robust Node.js API for scraping news from multiple Bangladeshi news websites with proxy support, rate limiting, and authentication.

## Features

- **Multi-provider support**: Prothom Alo, Ittefaq, <PERSON><PERSON>, <PERSON><PERSON>
- **Proxy rotation**: Configurable proxy support to avoid bot detection
- **Rate limiting**: Configurable request limits per IP
- **Authentication**: Bearer token authentication
- **Pagination**: Support for paginated results
- **Category filtering**: Filter news by categories
- **Consistent API**: Unified response format across all providers
- **Extensible**: Easy to add new news providers

## Installation

1. Install dependencies:

```bash
npm install
```

2. Configure environment variables in `.env`:

```env
PORT=3000
API_PASSWORD=your_secure_password_here
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
PROXY_ROTATION=true
PROXY_TIMEOUT=10000
LOG_LEVEL=info
```

3. Configure proxies in `src/config/proxies.json` (optional):

```json
{
  "proxies": [
    {
      "host": "proxy1.example.com",
      "port": 8080,
      "username": "user1",
      "password": "pass1"
    }
  ]
}
```

## Usage

### Start the server:

```bash
npm run dev  # Development with auto-reload
npm start    # Production
```

### API Endpoints

#### Get News

```
GET /api/v1/news/:provider?category=sports&page=1&limit=10
Authorization: Bearer YOUR_API_PASSWORD
```

**Providers**: `prothomalo`, `ittefaq`, `kalerkantho`

**Parameters**:

- `category` (optional): News category
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 50)

#### List Providers

```
GET /api/v1/providers
Authorization: Bearer YOUR_API_PASSWORD
```

#### Health Check

```
GET /health
```

### Example Response

```json
{
  "success": true,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "provider": "prothomalo",
  "category": "sports",
  "page": 1,
  "limit": 10,
  "total": 50,
  "articles": [
    {
      "id": "article-123",
      "title": "News Title",
      "description": "News description...",
      "url": "https://example.com/article",
      "publishedAt": "2024-01-01T00:00:00.000Z",
      "category": "sports",
      "author": "Author Name",
      "imageUrl": "https://example.com/image.jpg"
    }
  ]
}
```

## Adding New Scrapers

1. Create a new scraper class extending `BaseScraper`:

```javascript
// src/scrapers/newsite.js
import { BaseScraper } from './base.js';

export class NewSiteScraper extends BaseScraper {
  constructor() {
    super('NewSite');
    this.baseUrl = 'newsite.com';
  }

  async scrapeNews(category, page, limit) {
    // Implementation
  }
}
```

2. Register in `src/scrapers/index.js`:

```javascript
import { NewSiteScraper } from './newsite.js';

export const scrapers = {
  // ... existing scrapers
  newsite: new NewSiteScraper(),
};
```

3. Update validation in `src/utils/validator.js`

## Security Features

- **Authentication**: Bearer token required for all scraping endpoints
- **Rate limiting**: Configurable per-IP request limits
- **Proxy rotation**: Helps avoid IP-based blocking
- **Input validation**: Validates all parameters
- **Error handling**: Comprehensive error handling and logging

## Logging

Logs are written to:

- Console (formatted)
- `./logs/scraper.log` (rotating daily, 7-day retention)

## Rate Limiting

Default: 100 requests per 15 minutes per IP
Configurable via environment variables.

## License

MIT License
