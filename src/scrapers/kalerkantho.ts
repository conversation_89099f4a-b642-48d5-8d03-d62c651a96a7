import { BaseScraper } from './base';
import type { ScrapedNews } from '../types/index';

export class KalerKanthoScraper extends BaseScraper {
  constructor() {
    super('KalerKantho');
    this.baseUrl = 'kalerkantho.com';
  }

  async scrapeNews(
    category = 'sport',
    page = 1,
    limit = 10
  ): Promise<ScrapedNews> {
    const url = `https://bn.api-kalerkantho.com/api/online/${category}?page=${page}`;

    try {
      const data = await this.makeRequest(url);

      console.log(data);

      if (!data.category.data || !Array.isArray(data.category.data)) {
        throw new Error('Invalid response format');
      }

      const articles = data.category.data.slice(0, limit).map((item: any) =>
        this.normalizeArticle({
          id: item.n_id,
          title: item.n_head,
          description: item.n_details,
          url: `https://www.kalerkantho.com/online/${item.cat_name.slug}/${item.f_date}/${item.n_id}`,
          publishedAt: item.created_at,
          category: item.cat_name.slug,
          author: '',
          image: item.main_image || item.thumb_image,
        })
      );

      return {
        provider: 'kalerkantho',
        category,
        page,
        limit,
        total: data.total || articles.length,
        articles,
      };
    } catch (error) {
      throw new Error(
        `KalerKantho scraping failed: ${(error as Error).message}`
      );
    }
  }
}
