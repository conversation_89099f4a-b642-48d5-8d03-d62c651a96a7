import { ProthomAloScraper } from './prothomalo';
import { IttefaqScraper } from './ittefaq';
import { KalerKanthoScraper } from './kalerkantho';
import type { BaseScraper } from './base';

export const scrapers: Record<string, BaseScraper> = {
  prothomalo: new ProthomAloScraper(),
  ittefaq: new IttefaqScraper(),
  kalerkantho: new KalerKanthoScraper(),
};

export const getScraper = (provider: string): BaseScraper | undefined => {
  return scrapers[provider.toLowerCase()];
};
