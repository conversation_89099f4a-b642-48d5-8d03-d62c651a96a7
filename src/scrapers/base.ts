import axios from 'axios';
import { proxyManager } from '../utils/proxy';
import { logger } from '../utils/logger';
import type { Article, ScrapedNews } from '../types/index';

export abstract class BaseScraper {
  protected name: string;
  protected baseUrl: string = '';
  protected maxRetries = 3;

  constructor(name: string) {
    this.name = name;
  }

  protected async makeRequest(url: string, options: any = {}): Promise<any> {
    let lastError: Error;

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        const proxy =
          process.env.PROXY_ROTATION === 'true'
            ? proxyManager.getNextProxy()
            : null;
        const config = {
          ...proxyManager.getAxiosConfig(proxy),
          ...options,
        };

        logger.info(
          `${this.name}: Attempting request to ${url} (attempt ${attempt + 1})`
        );
        const response = await axios.get(url, config);

        logger.info(`${this.name}: Successfully fetched data`);
        return response.data;
      } catch (error) {
        lastError = error as Error;
        logger.warn(
          `${this.name}: Request failed (attempt ${attempt + 1}): ${
            lastError.message
          }`
        );

        if (attempt < this.maxRetries - 1) {
          await this.delay(1000 * (attempt + 1));
        }
      }
    }

    throw new Error(
      `${this.name}: All retry attempts failed. Last error: ${
        lastError!.message
      }`
    );
  }

  protected async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  protected normalizeArticle(article: any): Article {
    return {
      id: article.id || article.slug || this.generateId(article.title),
      title: this.cleanText(article.title),
      description: this.cleanText(article.description || article.excerpt || ''),
      url: article.url || article.link,
      publishedAt: this.normalizeDate(
        article.publishedAt || article.published_at || article.date
      ),
      category: article.category || '',
      author: article.author || '',
      imageUrl: this.normalizeImageUrl(
        article.image || article.imageUrl || article.thumbnail
      ),
    };
  }

  protected cleanText(text: string): string {
    if (!text) return '';
    return text.replace(/\s+/g, ' ').trim();
  }

  protected normalizeDate(date: any): string | null {
    if (!date) return null;
    try {
      return new Date(date).toISOString();
    } catch {
      return null;
    }
  }

  protected normalizeImageUrl(url: string): string | null {
    if (!url) return null;
    if (url.startsWith('//')) return `https:${url}`;
    if (url.startsWith('/')) return `https://${this.baseUrl}${url}`;
    return url;
  }

  protected generateId(title: string): string {
    return (
      title
        ?.toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .substring(0, 50) || Math.random().toString(36)
    );
  }

  abstract scrapeNews(
    category?: string,
    page?: number,
    limit?: number
  ): Promise<ScrapedNews>;
}
