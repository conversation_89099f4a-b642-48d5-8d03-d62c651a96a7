import { BaseScraper } from './base';
import type { ScrapedNews } from '../types/index';
import { JSDOM } from 'jsdom';
import fs from 'fs/promises';

export class IttefaqScraper extends BaseScraper {
  constructor() {
    super('Ittefaq');
    this.baseUrl = 'en.ittefaq.com.bd';
  }

  private getCategoryWidget(category: string): number {
    const widgets: Record<string, number> = {
      politics: 28,
      sports: 29,
      economy: 30,
      international: 31,
    };
    return widgets[category.toLowerCase()] || 28;
  }

  async scrapeNews(
    category = 'politics',
    page = 1,
    limit = 9
  ): Promise<ScrapedNews> {
    const start = (page - 1) * limit;
    const widget = this.getCategoryWidget(category);

    const url = `https://en.ittefaq.com.bd/api/theme_engine/get_ajax_contents?widget=${widget}&start=${start}&count=${limit}&page_id=1093&subpage_id=0&author=0&tags=&archive_time=&filter=`;

    const headers = {
      Referer: `https://en.ittefaq.com.bd/${category}`,
      'X-Requested-With': 'XMLHttpRequest',
      Accept: 'application/json, text/javascript, */*; q=0.01',
    };

    try {
      const data = await this.makeRequest(url, { headers });
      await fs.writeFile('ittefaq.html', data.html);

      console.log(data);

      if (!data.html) {
        throw new Error('Invalid response format');
      }

      const articles = this.parseIttefaqContent(data.html, category);

      return {
        provider: 'ittefaq',
        category,
        page,
        limit,
        total: articles.length,
        articles,
      };
    } catch (error) {
      throw new Error(`Ittefaq scraping failed: ${(error as Error).message}`);
    }
  }

  private parseIttefaqContent(htmlContent: string, category: string) {
    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;
    const articleElements = document.querySelectorAll('.each.col_in');

    const articles: any[] = [];

    articleElements.forEach((articleEl, index) => {
      const anchor = articleEl.querySelector('.title a.link_overlay');
      const imageSpan = articleEl.querySelector('span[data-ari]');

      if (anchor) {
        const title = anchor.textContent?.trim() || '';
        const href = anchor.getAttribute('href') || '';

        // Extract image from data-ari attribute
        let image = null;
        if (imageSpan) {
          const dataAri = imageSpan.getAttribute('data-ari');
          if (dataAri) {
            try {
              const ariData = JSON.parse(dataAri);
              image = ariData.path
                ? `https://en.ittefaq.com.bd/${ariData.path}`
                : null;
            } catch (e) {
              // Ignore JSON parse errors
            }
          }
        }

        articles.push(
          this.normalizeArticle({
            id: `ittefaq_${category}_${index}`,
            title,
            description: '',
            url: href.startsWith('http')
              ? href
              : `https://en.ittefaq.com.bd${href}`,
            publishedAt: new Date().toISOString(),
            category,
            author: '',
            image,
          })
        );
      }
    });

    return articles;
  }
}
