import { BaseScraper } from './base';
import type { ScrapedNews } from '../types/index';

export class ProthomAloScraper extends BaseScraper {
  constructor() {
    super('ProthomAlo');
    this.baseUrl = 'en.prothomalo.com';
  }

  async scrapeNews(
    category = 'sports',
    page = 1,
    limit = 10
  ): Promise<ScrapedNews> {
    const offset = (page - 1) * limit;
    const url = `https://en.prothomalo.com/api/v1/collections/${category}?offset=${offset}&limit=${limit}`;

    try {
      const data = await this.makeRequest(url);

      if (!Array.isArray(data.items)) {
        throw new Error('Invalid response format');
      }

      console.log(data.items[0]);

      const articles = data.items.map((item: any) =>
        this.normalizeArticle({
          id: item.story.id,
          title: item.story.headline,
          description: item.story.metadata.excerpt,
          url: item.story.url,
          publishedAt: item.story['published-at'],
          category: category,
          author: item.story['author-name'],
          image: item.story['hero-image-s3-key'],
        })
      );

      return {
        provider: 'prothomalo',
        category,
        page,
        limit,
        total: data.total || articles.length,
        articles,
      };
    } catch (error) {
      throw new Error(
        `ProthomAlo scraping failed: ${(error as Error).message}`
      );
    }
  }
}
