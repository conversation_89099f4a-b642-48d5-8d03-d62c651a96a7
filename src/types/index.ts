export interface Article {
  id: string;
  title: string;
  description: string;
  url: string;
  publishedAt: string | null;
  category: string;
  author: string;
  imageUrl: string | null;
}

export interface ScrapedNews {
  provider: string;
  category: string;
  page: number;
  limit: number;
  total: number;
  articles: Article[];
}

export interface ProxyConfig {
  host: string;
  port: number;
  username?: string;
  password?: string;
}

export interface ProxySettings {
  proxies: ProxyConfig[];
  rotation: {
    strategy: string;
    failover: boolean;
    maxRetries: number;
  };
}

export interface ApiResponse<T = any> {
  success?: boolean;
  timestamp?: string;
  error?: string;
  message?: string;
  data?: T;
}