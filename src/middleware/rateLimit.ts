import { logger } from '../utils/logger';

class RateLimiter {
  private requests = new Map<string, number[]>();
  private maxRequests: number;
  private windowMs: number;

  constructor() {
    this.maxRequests = parseInt(process.env.RATE_LIMIT_MAX || '100');
    this.windowMs = parseInt(process.env.RATE_LIMIT_WINDOW || '900000'); // 15 minutes
  }

  isAllowed(ip: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;

    if (!this.requests.has(ip)) {
      this.requests.set(ip, []);
    }

    const userRequests = this.requests.get(ip)!;

    // Remove old requests outside the window
    const recentRequests = userRequests.filter(
      (timestamp) => timestamp > windowStart
    );
    this.requests.set(ip, recentRequests);

    if (recentRequests.length >= this.maxRequests) {
      return false;
    }

    // Add current request
    recentRequests.push(now);
    return true;
  }
}

const rateLimiter = new RateLimiter();

export const rateLimitMiddleware = ({ request, set }: any) => {
  const ip =
    request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    'unknown';

  if (!rateLimiter.isAllowed(ip)) {
    logger.warn(`Rate limit exceeded for IP: ${ip}`);
    set.status = 429;
    return {
      error: 'Rate limit exceeded',
      message: 'Too many requests, please try again later',
    };
  }
};
