import { logger } from '../utils/logger';

export const authMiddleware = (password: string) => {
  return ({ headers, set }: any) => {
    const authHeader = headers.authorization;

    if (authHeader !== password) {
      logger.warn('Unauthorized access attempt: Invalid token');
      set.status = 401;
      return {
        error: 'Unauthorized',
        message: 'Invalid token',
      };
    }
  };
};
