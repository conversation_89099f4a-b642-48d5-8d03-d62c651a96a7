import { serve } from '@hono/node-server';
import { app } from './app';
import 'dotenv/config';
import { logger } from './utils/logger';

serve(
  {
    fetch: app.fetch,
    port: Number(process.env.PORT) || 3000,
  },
  () => {
    logger.info(
      `🚀 News Scraper API running on port ${process.env.PORT || 3000}`
    );
    logger.info('📖 Available endpoints:');
    logger.info('  GET / - API documentation');
    logger.info('  GET /health - Health check');
    logger.info(
      '  GET /api/v1/news/:provider - Get news (requires authentication)'
    );
    logger.info('  GET /api/v1/providers - List supported providers');

    console.log(
      `🚀 News Scraper API running on http://localhost:${
        process.env.PORT || 3000
      }`
    );
  }
);
