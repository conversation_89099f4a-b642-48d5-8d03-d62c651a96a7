import { Elysia } from 'elysia';
import { getScraper } from './scrapers/index';
import {
  validateProvider,
  validateCategory,
  validatePagination,
} from './utils/validator';
import { authMiddleware } from './middleware/auth';
import { rateLimitMiddleware } from './middleware/rateLimit';
import { logger } from './utils/logger';

export const app = new Elysia()
  .onError(({ error, set }: any) => {
    logger.error('API Error:', error);
    set.status = 500;
    return {
      error: 'Internal Server Error',
      message: error.message,
    };
  })
  .get('/', () => ({
    message: 'News Scraper API',
    version: '1.0.0',
    endpoints: {
      '/news/:provider': 'Get news from a specific provider',
      '/health': 'Health check endpoint',
    },
    providers: ['prothomalo', 'ittefaq', 'kalerkantho'],
  }))
  .get('/health', () => ({
    status: 'healthy',
    timestamp: new Date().toISOString(),
  }))
  .group('/api/v1', (app) =>
    app
      .derive(rateLimitMiddleware)
      .derive(authMiddleware(process.env.API_PASSWORD || 'abcd'))
      .get('/news/:provider', async ({ params, query, set }) => {
        const { provider } = params;
        const { category, page = 1, limit = 10 } = query;

        // Validate provider
        if (!validateProvider(provider)) {
          set.status = 400;
          return {
            error: 'Invalid provider',
            message:
              'Supported providers: prothomalo, ittefaq, nayadiganta, kalerkantho',
          };
        }

        // Validate category if provided
        if (category && !validateCategory(provider, category)) {
          set.status = 400;
          return {
            error: 'Invalid category',
            message: `Category '${category}' not supported for provider '${provider}'`,
          };
        }

        // Validate pagination
        const { page: validPage, limit: validLimit } = validatePagination(
          page,
          limit
        );

        try {
          const scraper = getScraper(provider);
          if (!scraper) {
            set.status = 404;
            return {
              error: 'Scraper not found',
              message: `No scraper available for provider '${provider}'`,
            };
          }

          const result = await scraper.scrapeNews(
            category,
            validPage,
            validLimit
          );

          logger.info(
            `Successfully scraped ${result.articles.length} articles from ${provider}`
          );

          return {
            success: true,
            timestamp: new Date().toISOString(),
            ...result,
          };
        } catch (error) {
          logger.error(`Scraping error for ${provider}:`, error);
          set.status = 500;
          return {
            error: 'Scraping failed',
            message: (error as Error).message,
            provider,
          };
        }
      })
      .get('/providers', () => ({
        providers: {
          prothomalo: {
            name: 'Prothom Alo (English)',
            categories: [
              'sports',
              'politics',
              'business',
              'entertainment',
              'international',
            ],
          },
          ittefaq: {
            name: 'Ittefaq (English)',
            categories: ['politics', 'sports', 'economy', 'international'],
          },

          kalerkantho: {
            name: 'Kaler Kantho (Bengali)',
            categories: ['sport', 'politics', 'entertainment', 'national'],
          },
        },
      }))
  );
