export const validateProvider = (provider: string): boolean => {
  const validProviders = ['prothomalo', 'ittefaq', 'kalerkantho'];
  return validProviders.includes(provider.toLowerCase());
};

export const validateCategory = (
  provider: string,
  category: string
): boolean => {
  const categoryMap: Record<string, string[]> = {
    prothomalo: [
      'sports',
      'politics',
      'business',
      'entertainment',
      'international',
    ],
    ittefaq: ['politics', 'sports', 'economy', 'international'],
    kalerkantho: ['sport', 'politics', 'entertainment', 'national'],
  };

  return (
    categoryMap[provider.toLowerCase()]?.includes(category.toLowerCase()) ||
    false
  );
};

export const validatePagination = (
  page: string | number,
  limit: string | number
) => {
  const pageNum = parseInt(page.toString()) || 1;
  const limitNum = parseInt(limit.toString()) || 10;

  return {
    page: Math.max(1, Math.min(pageNum, 100)),
    limit: Math.max(1, Math.min(limitNum, 50)),
  };
};
