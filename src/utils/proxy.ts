import fs from 'fs';
import path from 'path';
import { logger } from './logger';
import type { ProxyConfig, ProxySettings } from '../types/index';

interface AxiosProxyConfig {
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
}

class ProxyManager {
  private proxies: ProxyConfig[] = [];
  private currentIndex = 0;
  private failedProxies = new Set<number>();

  constructor() {
    this.loadProxies();
  }

  private loadProxies(): void {
    try {
      const configPath = path.join(process.cwd(), 'src/config/proxieson');
      const config: ProxySettings = JSON.parse(
        fs.readFileSync(configPath, 'utf8')
      );
      this.proxies = config.proxies || [];
      logger.info(`Loaded ${this.proxies.length} proxies`);
    } catch (error) {
      logger.warn('No proxy configuration found, running without proxies');
      this.proxies = [];
    }
  }

  getNextProxy(): AxiosProxyConfig | null {
    if (this.proxies.length === 0) return null;

    const availableProxies = this.proxies.filter(
      (_, index) => !this.failedProxies.has(index)
    );

    if (availableProxies.length === 0) {
      this.failedProxies.clear();
      logger.info('Reset failed proxies list');
    }

    const proxy = this.proxies[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.proxies.length;

    return {
      host: proxy.host,
      port: proxy.port,
      ...(proxy.username &&
        proxy.password && {
          auth: {
            username: proxy.username,
            password: proxy.password,
          },
        }),
    };
  }

  markProxyFailed(proxyIndex: number): void {
    this.failedProxies.add(proxyIndex);
    logger.warn(`Marked proxy ${proxyIndex} as failed`);
  }

  getAxiosConfig(proxy: AxiosProxyConfig | null = null) {
    const config: any = {
      timeout: parseInt(process.env.PROXY_TIMEOUT || '10000'),
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        Accept: 'application/json, text/html, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    };

    if (proxy) {
      config.proxy = proxy;
    }

    return config;
  }
}

export const proxyManager = new ProxyManager();
